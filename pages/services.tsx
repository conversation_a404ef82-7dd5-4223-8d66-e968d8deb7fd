import Head from "next/head";
import HeaderSection from "../components/HeaderSection";
import Container from "../components/Container";
import Card from "../components/Card";
import Footer from "../components/FooterSection";
import Button from "../components/Button";

export default function ServicesPage() {
    const isoStandards = [
        "ISO 9001 (Quality Management)",
        "ISO 27001 (Information Security)", 
        "ISO 14001 (Environmental Management)",
        "ISO 45001 (Occupational Health & Safety)",
        "ISO 27701 (Privacy Information Management)",
        "ISO 20000-1 (IT Service Management)",
        "ISO 21001 (Educational Organizations)",
        "ISO 22301 (Business Continuity)",
        "ISO 42001 (AI Management Systems) - Early Adopter"
    ];

    const socCriteria = [
        { name: "Security", description: "Protection against unauthorized access" },
        { name: "Availability", description: "System operation and availability" },
        { name: "Confidentiality", description: "Protection of confidential information" },
        { name: "Processing Integrity", description: "Complete, valid, accurate processing" },
        { name: "Privacy", description: "Personal information handling" }
    ];

    return (
        <>
            <Head>
                <title>Services - NACE Certification</title>
                <meta name="description" content="Complete assurance & enablement portfolio. ISO certification, SOC 2 audits, integrated programs, and professional training." />
                <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
            </Head>
            <HeaderSection />
            <main className="mb-40">
                {/* Services Hero Section */}
                <div className="relative overflow-hidden dark:bg-darker pt-40 xl:pt-36" id="services">
                    <Container>
                        <div className="text-center">
                            <h1 className="text-5xl font-black dark:text-white md:text-6xl xl:text-7xl">
                                Complete Assurance & Enablement Portfolio
                            </h1>
                            <p className="mt-8 text-lg text-gray-700 dark:text-gray-100 max-w-4xl mx-auto">
                                NACE Certification delivers a complete, end-to-end portfolio of assurance and enablement services. Whether you need a single ISO certificate or an integrated, multi-standard program, our experts—and our portal—make the journey clear, fast, and impartial.
                            </p>
                            
                            <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
                                <div className="text-center">
                                    <h3 className="text-xl font-semibold text-primary dark:text-white mb-2">Single ISO Certificate</h3>
                                    <p className="text-gray-600 dark:text-gray-300">Individual standard certification</p>
                                </div>
                                <div className="text-center">
                                    <h3 className="text-xl font-semibold text-primary dark:text-white mb-2">Integrated Programs</h3>
                                    <p className="text-gray-600 dark:text-gray-300">Multi-standard approach</p>
                                </div>
                                <div className="text-center">
                                    <h3 className="text-xl font-semibold text-primary dark:text-white mb-2">Digital Portal</h3>
                                    <p className="text-gray-600 dark:text-gray-300">Clear, fast, and impartial</p>
                                </div>
                            </div>
                        </div>
                    </Container>
                </div>

                {/* ISO Certification Section */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="text-center mb-16">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white md:text-4xl lg:text-5xl">
                                Independent Third-Party Audits
                            </h2>
                            <p className="mt-4 text-gray-600 dark:text-gray-300">
                                Comprehensive ISO certification services across all major management system standards.
                            </p>
                        </div>

                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                            {isoStandards.map((standard, index) => (
                                <Card key={index}>
                                    <div className="text-center">
                                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                            {standard}
                                        </h3>
                                    </div>
                                </Card>
                            ))}
                        </div>
                    </Container>
                </div>

                {/* SOC Assurance Section */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="text-center mb-16">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white md:text-4xl lg:text-5xl">
                                AICPA Trust Services Criteria
                            </h2>
                            <p className="mt-4 text-gray-600 dark:text-gray-300">
                                SOC 2 Type II audits covering all five trust services criteria.
                            </p>
                        </div>

                        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                            {socCriteria.map((criteria, index) => (
                                <Card key={index}>
                                    <div>
                                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                                            {criteria.name}
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-400">
                                            {criteria.description}
                                        </p>
                                    </div>
                                </Card>
                            ))}
                        </div>

                        <div className="mt-12 text-center">
                            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Benefits</h3>
                            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                                <div>
                                    <h4 className="font-semibold text-gray-900 dark:text-white">Enterprise Sales</h4>
                                    <p className="text-sm text-gray-600 dark:text-gray-300">Accelerate B2B sales cycles</p>
                                </div>
                                <div>
                                    <h4 className="font-semibold text-gray-900 dark:text-white">Investor Due Diligence</h4>
                                    <p className="text-sm text-gray-600 dark:text-gray-300">Meet investor requirements</p>
                                </div>
                                <div>
                                    <h4 className="font-semibold text-gray-900 dark:text-white">Market Access</h4>
                                    <p className="text-sm text-gray-600 dark:text-gray-300">Essential for U.S. SaaS and fintech markets</p>
                                </div>
                                <div>
                                    <h4 className="font-semibold text-gray-900 dark:text-white">Competitive Edge</h4>
                                    <p className="text-sm text-gray-600 dark:text-gray-300">Differentiate from competitors</p>
                                </div>
                            </div>
                        </div>
                    </Container>
                </div>

                {/* Integrated Audits Section */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="text-center mb-16">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white md:text-4xl lg:text-5xl">
                                Integrated Audits
                            </h2>
                            <p className="mt-4 text-gray-600 dark:text-gray-300">
                                Multi-standard certification approach for maximum efficiency and cost savings.
                            </p>
                        </div>

                        <div className="grid gap-8 md:grid-cols-2">
                            <div>
                                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Benefits of Integration</h3>
                                <div className="space-y-4">
                                    <div className="flex items-start space-x-3">
                                        <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center mt-1">
                                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-gray-900 dark:text-white">Multi-Standard Approach</h4>
                                            <p className="text-gray-600 dark:text-gray-300">Combine ISO 9001, 27001, 14001, and 45001 in one engagement</p>
                                        </div>
                                    </div>
                                    <div className="flex items-start space-x-3">
                                        <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center mt-1">
                                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-gray-900 dark:text-white">Cost-Effective Solutions</h4>
                                            <p className="text-gray-600 dark:text-gray-300">Up to 30% savings compared to separate audits</p>
                                        </div>
                                    </div>
                                    <div className="flex items-start space-x-3">
                                        <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center mt-1">
                                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-gray-900 dark:text-white">Reduced Audit Fatigue</h4>
                                            <p className="text-gray-600 dark:text-gray-300">Minimize disruption to business operations</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="bg-gradient-to-br from-primary/10 to-purple-400/10 rounded-lg p-8">
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Popular Combinations</h3>
                                <div className="space-y-3">
                                    <div className="bg-white dark:bg-gray-800 rounded p-3">
                                        <div className="font-medium text-gray-900 dark:text-white">Quality + Security</div>
                                        <div className="text-sm text-gray-600 dark:text-gray-300">ISO 9001 + ISO 27001</div>
                                    </div>
                                    <div className="bg-white dark:bg-gray-800 rounded p-3">
                                        <div className="font-medium text-gray-900 dark:text-white">Environmental + Safety</div>
                                        <div className="text-sm text-gray-600 dark:text-gray-300">ISO 14001 + ISO 45001</div>
                                    </div>
                                    <div className="bg-white dark:bg-gray-800 rounded p-3">
                                        <div className="font-medium text-gray-900 dark:text-white">Complete Management</div>
                                        <div className="text-sm text-gray-600 dark:text-gray-300">ISO 9001 + 27001 + 14001 + 45001</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Container>
                </div>

                {/* Gap & Readiness Section */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="grid gap-12 md:grid-cols-2 md:items-center">
                            <div>
                                <h2 className="text-3xl font-bold text-gray-900 dark:text-white md:text-4xl lg:text-5xl mb-6">
                                    Gap & Readiness Assessment
                                </h2>
                                <p className="text-gray-600 dark:text-gray-300 mb-6">
                                    Pre-certification assessments to identify gaps and ensure readiness before your formal audit.
                                </p>
                                <div className="space-y-4">
                                    <div>• Comprehensive documentation review</div>
                                    <div>• Process effectiveness evaluation</div>
                                    <div>• Risk assessment and mitigation planning</div>
                                    <div>• Readiness scoring and recommendations</div>
                                    <div>• Action plan development</div>
                                </div>
                            </div>
                            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-8">
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Assessment Benefits</h3>
                                <div className="space-y-3 text-gray-600 dark:text-gray-300">
                                    <div>✓ Increased first-time certification success rate</div>
                                    <div>✓ Reduced audit findings and non-conformities</div>
                                    <div>✓ Better preparation and confidence</div>
                                    <div>✓ Faster certification timeline</div>
                                    <div>✓ Cost-effective preparation strategy</div>
                                </div>
                            </div>
                        </div>
                    </Container>
                </div>

                {/* Training Section */}
                <div className="pt-32 md:pt-44" id="training">
                    <Container>
                        <div className="text-center mb-16">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white md:text-4xl lg:text-5xl">
                                Professional Development & Training
                            </h2>
                            <p className="mt-4 text-gray-600 dark:text-gray-300">
                                Comprehensive training programs to advance your certification expertise.
                            </p>
                        </div>

                        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
                            <Card>
                                <div className="text-center">
                                    <div className="text-3xl mb-4">🎓</div>
                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Lead Auditor Courses</h3>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">ISO 9001, 27001, 14001, 45001 lead auditor certification programs</p>
                                </div>
                            </Card>
                            <Card>
                                <div className="text-center">
                                    <div className="text-3xl mb-4">💻</div>
                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Virtual Training</h3>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">Interactive online sessions with global accessibility</p>
                                </div>
                            </Card>
                            <Card>
                                <div className="text-center">
                                    <div className="text-3xl mb-4">🏢</div>
                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">On-site Training</h3>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">Customized corporate training at your location</p>
                                </div>
                            </Card>
                            <Card>
                                <div className="text-center">
                                    <div className="text-3xl mb-4">📚</div>
                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Self-paced Learning</h3>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">Flexible online modules with progress tracking</p>
                                </div>
                            </Card>
                        </div>
                    </Container>
                </div>

                {/* Digital Services Section */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="text-center mb-16">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white md:text-4xl lg:text-5xl">
                                Technology-Enabled Services
                            </h2>
                            <p className="mt-4 text-gray-600 dark:text-gray-300">
                                Leveraging technology to deliver superior certification experiences.
                            </p>
                        </div>

                        <div className="grid gap-8 md:grid-cols-3">
                            <div className="text-center">
                                <div className="text-5xl mb-4">📱</div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">Digital Portal</h3>
                                <p className="text-gray-600 dark:text-gray-300">Real-time audit tracking, document management, and progress monitoring</p>
                            </div>
                            <div className="text-center">
                                <div className="text-5xl mb-4">🔒</div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">Secure Evidence Vault</h3>
                                <p className="text-gray-600 dark:text-gray-300">Encrypted storage and management of audit evidence and documentation</p>
                            </div>
                            <div className="text-center">
                                <div className="text-5xl mb-4">📊</div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">QR-Coded Certificates</h3>
                                <p className="text-gray-600 dark:text-gray-300">Instant verification and authenticity checking for all certificates</p>
                            </div>
                        </div>
                    </Container>
                </div>

                {/* How to Engage Section */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="bg-gradient-to-r from-primary to-purple-600 rounded-3xl p-8 md:p-12 text-center text-white">
                            <h2 className="text-3xl font-bold md:text-4xl lg:text-5xl mb-6">
                                Ready to Begin Your Certification Journey?
                            </h2>
                            <p className="text-lg md:text-xl mb-8 opacity-90 max-w-3xl mx-auto">
                                Start with a consultation to understand your needs and develop a customized certification strategy.
                            </p>
                            
                            <div className="grid gap-6 md:grid-cols-4 mb-8 text-center">
                                <div className="bg-white/10 rounded-lg p-4">
                                    <div className="text-2xl mb-2">1️⃣</div>
                                    <div className="font-semibold">Initial Consultation</div>
                                </div>
                                <div className="bg-white/10 rounded-lg p-4">
                                    <div className="text-2xl mb-2">2️⃣</div>
                                    <div className="font-semibold">Scope Definition</div>
                                </div>
                                <div className="bg-white/10 rounded-lg p-4">
                                    <div className="text-2xl mb-2">3️⃣</div>
                                    <div className="font-semibold">Proposal & Timeline</div>
                                </div>
                                <div className="bg-white/10 rounded-lg p-4">
                                    <div className="text-2xl mb-2">4️⃣</div>
                                    <div className="font-semibold">Audit Execution</div>
                                </div>
                            </div>

                            <Button
                                Element="a"
                                emphasis="secondary"
                                label="Request Consultation"
                                to="/contact"
                                ui="max"
                            />
                        </div>
                    </Container>
                </div>
            </main>
            <Footer />
        </>
    );
}