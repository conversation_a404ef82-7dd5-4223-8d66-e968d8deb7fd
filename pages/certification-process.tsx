import Head from "next/head";
import HeaderSection from "../components/HeaderSection";
import Container from "../components/Container";
import Footer from "../components/FooterSection";
import Button from "../components/Button";
import Card from "../components/Card";





export default function CertificationProcessPage() {
    const stages = [
        {
            number: "1",
            title: "Application & Scoping",
            description: "We confirm objectives, locations, and boundaries. Initial application review and contract establishment.",
            duration: "1-2 weeks"
        },
        {
            number: "2", 
            title: "Stage 1 Audit (Readiness Review)",
            description: "Document study & gap analysis. Review of management system documentation and readiness assessment.",
            duration: "1-2 weeks"
        },
        {
            number: "3",
            title: "Stage 2 Audit (Certification Audit)", 
            description: "Process verification & evidence sampling. On-site or remote audit of implemented management system.",
            duration: "2-3 weeks"
        },
        {
            number: "4",
            title: "Certification Decision",
            description: "Independent technical review and certification decision. Digital certificate issuance upon successful completion.",
            duration: "1-2 weeks"
        },
        {
            number: "5",
            title: "Surveillance Audits",
            description: "Annual surveillance audits to maintain certification validity and monitor continued compliance.",
            duration: "Ongoing"
        },
        {
            number: "6",
            title: "Recertification",
            description: "Full recertification audit every 3 years to renew the certificate for another 3-year cycle.",
            duration: "Every 3 years"
        }
    ];

    return (
        <>
            <Head>
                <title>Certification Process - NACE Certification</title>
                <meta name="description" content="Clear. Predictable. Built for speed. Learn about our 6-stage certification process that follows ISO/IEC 17021-1 requirements with digital portal integration." />
                <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
            </Head>
            <HeaderSection />
            <main className="mb-40">
                {/* Process Hero Section */}
                <div className="relative overflow-hidden dark:bg-darker pt-40 xl:pt-36" id="process">
                    <Container>
                        <div className="text-center">
                            <h1 className="text-5xl font-black dark:text-white md:text-6xl xl:text-7xl">
                                Clear. Predictable. Built for speed.
                            </h1>
                            <p className="mt-8 text-lg text-gray-700 dark:text-gray-100 max-w-4xl mx-auto">
                                Learn about our 6-stage certification process that follows ISO/IEC 17021-1 requirements with digital portal integration.
                            </p>
                        </div>
                    </Container>
                </div>

                {/* Six Stages Section */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="text-center mb-16">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white md:text-4xl lg:text-5xl">
                                Six-Stage Certification Process
                            </h2>
                            <p className="mt-4 text-gray-600 dark:text-gray-300">
                                A systematic approach ensuring thorough evaluation and continuous improvement.
                            </p>
                        </div>

                        <div className="space-y-12">
                            {stages.map((stage, index) => (
                                <div key={index} className="relative">
                                    <div className="flex items-start space-x-6">
                                        <div className="flex-shrink-0">
                                            <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary text-white font-bold text-xl">
                                                {stage.number}
                                            </div>
                                        </div>
                                        <div className="flex-1">
                                            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                                                <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">
                                                    {stage.title}
                                                </h3>
                                                <span className="text-sm font-medium text-primary bg-primary/10 px-3 py-1 rounded-full mt-2 md:mt-0 w-fit">
                                                    {stage.duration}
                                                </span>
                                            </div>
                                            <p className="text-gray-600 dark:text-gray-400 text-lg">
                                                {stage.description}
                                            </p>
                                        </div>
                                    </div>
                                    {index < stages.length - 1 && (
                                        <div className="ml-8 mt-4 h-8 w-0.5 bg-gray-300 dark:bg-gray-600"></div>
                                    )}
                                </div>
                            ))}
                        </div>
                    </Container>
                </div>

                {/* Timeline Snapshot */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="text-center mb-16">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white md:text-4xl lg:text-5xl">
                                Timeline Snapshot
                            </h2>
                            <p className="mt-4 text-gray-600 dark:text-gray-300">
                                Typical certification timeline from application to certificate issuance.
                            </p>
                        </div>

                        <div className="bg-gradient-to-r from-primary/10 to-purple-400/10 rounded-3xl p-8 md:p-12">
                            <div className="grid gap-8 md:grid-cols-3 text-center">
                                <div>
                                    <div className="text-4xl font-bold text-primary dark:text-white mb-2">8-12</div>
                                    <div className="text-lg font-semibold text-gray-900 dark:text-white mb-1">Weeks</div>
                                    <div className="text-gray-600 dark:text-gray-300">Typical certification timeline</div>
                                </div>
                                <div>
                                    <div className="text-4xl font-bold text-primary dark:text-white mb-2">3</div>
                                    <div className="text-lg font-semibold text-gray-900 dark:text-white mb-1">Years</div>
                                    <div className="text-gray-600 dark:text-gray-300">Certificate validity period</div>
                                </div>
                                <div>
                                    <div className="text-4xl font-bold text-primary dark:text-white mb-2">24/7</div>
                                    <div className="text-lg font-semibold text-gray-900 dark:text-white mb-1">Portal Access</div>
                                    <div className="text-gray-600 dark:text-gray-300">Real-time tracking available</div>
                                </div>
                            </div>
                        </div>
                    </Container>
                </div>

                {/* Digital Portal Section */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="grid gap-12 md:grid-cols-2 md:items-center">
                            <div>
                                <h2 className="text-3xl font-bold text-gray-900 dark:text-white md:text-4xl lg:text-5xl">
                                    Digital Portal Integration
                                </h2>
                                <p className="mt-6 text-gray-600 dark:text-gray-300">
                                    Our proprietary digital portal provides complete transparency throughout the certification process.
                                </p>
                                <div className="mt-8 space-y-4">
                                    <div className="flex items-start space-x-3">
                                        <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mt-0.5">
                                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-gray-900 dark:text-white">Real-time Audit Tracking</h3>
                                            <p className="text-gray-600 dark:text-gray-300">Monitor progress at every stage</p>
                                        </div>
                                    </div>
                                    <div className="flex items-start space-x-3">
                                        <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mt-0.5">
                                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-gray-900 dark:text-white">Secure Evidence Vault</h3>
                                            <p className="text-gray-600 dark:text-gray-300">Centralized document management</p>
                                        </div>
                                    </div>
                                    <div className="flex items-start space-x-3">
                                        <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mt-0.5">
                                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-gray-900 dark:text-white">QR-Coded Certificates</h3>
                                            <p className="text-gray-600 dark:text-gray-300">Instant verification capabilities</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="bg-gradient-to-br from-primary/20 to-purple-400/20 rounded-3xl p-8 text-center">
                                <div className="text-6xl mb-4">📱</div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                                    Always Connected
                                </h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    Access your certification journey anytime, anywhere with our mobile-responsive portal.
                                </p>
                            </div>
                        </div>
                    </Container>
                </div>

                {/* Process Overview Section */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="text-center mb-16">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white md:text-4xl lg:text-5xl">
                                Process Overview
                            </h2>
                            <p className="mt-4 text-gray-600 dark:text-gray-300">
                                Our certification process follows ISO/IEC 17021-1 requirements while leveraging digital innovation for enhanced transparency.
                            </p>
                        </div>

                        <div className="bg-gradient-to-br from-primary/10 to-purple-400/10 rounded-3xl p-8 md:p-12">
                            <div className="grid gap-8 md:grid-cols-2">
                                <div>
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Key Principles</h3>
                                    <ul className="space-y-3 text-gray-600 dark:text-gray-300">
                                        <li>• Compliance with ISO/IEC 17021-1 standards</li>
                                        <li>• Risk-based audit approach</li>
                                        <li>• Continuous improvement focus</li>
                                        <li>• Stakeholder engagement throughout</li>
                                        <li>• Evidence-based decision making</li>
                                    </ul>
                                </div>
                                <div>
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Digital Integration</h3>
                                    <ul className="space-y-3 text-gray-600 dark:text-gray-300">
                                        <li>• Real-time progress tracking</li>
                                        <li>• Secure document management</li>
                                        <li>• Automated notifications and updates</li>
                                        <li>• Digital certificate issuance</li>
                                        <li>• Mobile-responsive portal access</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </Container>
                </div>

                {/* Roles & Responsibilities Section */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="text-center mb-16">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white md:text-4xl lg:text-5xl">
                                Roles & Responsibilities
                            </h2>
                            <p className="mt-4 text-gray-600 dark:text-gray-300">
                                Clear definition of responsibilities ensures smooth certification process execution.
                            </p>
                        </div>

                        <div className="grid gap-8 md:grid-cols-2">
                            <Card>
                                <div>
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Client Responsibilities</h3>
                                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                                        <li>• Implement and maintain management system</li>
                                        <li>• Provide access to personnel and facilities</li>
                                        <li>• Submit required documentation timely</li>
                                        <li>• Address non-conformities promptly</li>
                                        <li>• Maintain system effectiveness</li>
                                        <li>• Cooperate during audit activities</li>
                                        <li>• Pay certification fees as agreed</li>
                                    </ul>
                                </div>
                            </Card>
                            <Card>
                                <div>
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">NACE Responsibilities</h3>
                                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                                        <li>• Conduct impartial and competent audits</li>
                                        <li>• Provide qualified lead auditors</li>
                                        <li>• Maintain confidentiality of information</li>
                                        <li>• Issue certificates upon compliance</li>
                                        <li>• Conduct surveillance audits</li>
                                        <li>• Provide technical support and guidance</li>
                                        <li>• Maintain accreditation standards</li>
                                    </ul>
                                </div>
                            </Card>
                        </div>
                    </Container>
                </div>

                {/* Impartiality & Confidentiality Section */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="text-center mb-16">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white md:text-4xl lg:text-5xl">
                                Process Governance & Ethics
                            </h2>
                            <p className="mt-4 text-gray-600 dark:text-gray-300">
                                Our governance framework ensures impartiality, confidentiality, and ethical conduct throughout the certification process.
                            </p>
                        </div>

                        <div className="grid gap-8 md:grid-cols-3">
                            <div className="bg-gradient-to-br from-primary/10 to-purple-400/10 rounded-lg p-6">
                                <div className="text-3xl mb-4">⚖️</div>
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Impartiality</h3>
                                <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                    <li>• Independent audit teams</li>
                                    <li>• No consulting conflicts</li>
                                    <li>• Objective decision making</li>
                                    <li>• Regular impartiality reviews</li>
                                </ul>
                            </div>
                            <div className="bg-gradient-to-br from-primary/10 to-purple-400/10 rounded-lg p-6">
                                <div className="text-3xl mb-4">🔒</div>
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Confidentiality</h3>
                                <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                    <li>• Strict data protection</li>
                                    <li>• Secure information handling</li>
                                    <li>• Limited access protocols</li>
                                    <li>• Non-disclosure agreements</li>
                                </ul>
                            </div>
                            <div className="bg-gradient-to-br from-primary/10 to-purple-400/10 rounded-lg p-6">
                                <div className="text-3xl mb-4">🎯</div>
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Competence</h3>
                                <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                    <li>• Qualified auditor selection</li>
                                    <li>• Continuous training programs</li>
                                    <li>• Performance monitoring</li>
                                    <li>• Technical competence validation</li>
                                </ul>
                            </div>
                        </div>
                    </Container>
                </div>

                {/* Process FAQ Section */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="text-center mb-16">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white md:text-4xl lg:text-5xl">
                                Frequently Asked Questions
                            </h2>
                            <p className="mt-4 text-gray-600 dark:text-gray-300">
                                Common questions about our certification process and requirements.
                            </p>
                        </div>

                        <div className="max-w-4xl mx-auto space-y-6">
                            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">How long does the certification process take?</h3>
                                <p className="text-gray-600 dark:text-gray-300">Typically 8-12 weeks from application to certificate issuance, depending on organization size and complexity.</p>
                            </div>
                            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Can audits be conducted remotely?</h3>
                                <p className="text-gray-600 dark:text-gray-300">Yes, we offer flexible audit delivery including on-site, remote, and hybrid approaches based on your needs and standard requirements.</p>
                            </div>
                            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">What happens if non-conformities are found?</h3>
                                <p className="text-gray-600 dark:text-gray-300">You'll have the opportunity to address non-conformities through corrective actions. We provide guidance and verify effectiveness before certification.</p>
                            </div>
                            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">How often are surveillance audits required?</h3>
                                <p className="text-gray-600 dark:text-gray-300">Annual surveillance audits are required to maintain certification validity, with full recertification every three years.</p>
                            </div>
                            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Are your certificates internationally recognized?</h3>
                                <p className="text-gray-600 dark:text-gray-300">Yes, all certificates are issued under UAF accreditation scope and are recognized globally through international accreditation agreements.</p>
                            </div>
                        </div>
                    </Container>
                </div>

                {/* Ready to Begin Section */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="bg-gradient-to-r from-primary to-purple-600 rounded-3xl p-8 md:p-12 text-center text-white">
                            <h2 className="text-3xl font-bold md:text-4xl lg:text-5xl mb-6">
                                Ready to Begin Your Certification?
                            </h2>
                            <p className="text-lg md:text-xl mb-8 opacity-90 max-w-3xl mx-auto">
                                Take the first step towards certification excellence. Our team is ready to guide you through every stage of the process.
                            </p>
                            
                            <div className="grid gap-6 md:grid-cols-3 mb-8">
                                <div className="bg-white/10 rounded-lg p-6">
                                    <div className="text-3xl mb-3">📞</div>
                                    <h3 className="font-semibold mb-2">Free Consultation</h3>
                                    <p className="text-sm opacity-80">Discuss your certification needs with our experts</p>
                                </div>
                                <div className="bg-white/10 rounded-lg p-6">
                                    <div className="text-3xl mb-3">📋</div>
                                    <h3 className="font-semibold mb-2">Custom Proposal</h3>
                                    <p className="text-sm opacity-80">Receive a tailored certification plan and timeline</p>
                                </div>
                                <div className="bg-white/10 rounded-lg p-6">
                                    <div className="text-3xl mb-3">🚀</div>
                                    <h3 className="font-semibold mb-2">Fast Track Start</h3>
                                    <p className="text-sm opacity-80">Begin your certification journey within days</p>
                                </div>
                            </div>

                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <Button
                                    Element="a"
                                    emphasis="secondary"
                                    label="Start Your Certification"
                                    to="/contact"
                                    ui="max"
                                />
                                <Button
                                    Element="a"
                                    emphasis="secondary"
                                    label="Download Process Guide"
                                    to="/resources/process-guide"
                                    ui="max"
                                />
                            </div>
                        </div>
                    </Container>
                </div>
            </main>
            <Footer />
        </>
    );
}