import Head from "next/head";
import HeaderSection from "../../components/HeaderSection";
import Container from "../../components/Container";
import Button from "../../components/Button";
import Footer from "../../components/FooterSection";

export default function DocsIntroPage() {
    return (
        <>
            <Head>
                <title>Documentation - Welcome to PrismUI</title>
                <meta name="description" content="Introduction to PrismUI component library - A modern component library built on shadcn/ui" />
                <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
            </Head>
            <HeaderSection />
            <main className="mb-40">
                {/* Docs Hero Section */}
                <div className="relative overflow-hidden dark:bg-darker pt-40 xl:pt-36" id="docs">
                    <Container>
                        <div className="text-center">
                            <h1 className="text-5xl font-black dark:text-white md:text-6xl xl:text-7xl">
                                Welcome to PrismUI
                            </h1>
                            <p className="mt-8 text-lg text-gray-700 dark:text-gray-100 max-w-4xl mx-auto">
                                A modern component library built on shadcn/ui, inspired by <PERSON> Tey's design approach, giving you complete ownership and control over your code.
                            </p>
                        </div>
                    </Container>
                </div>

                {/* Introduction Section */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="max-w-4xl mx-auto">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
                                Introduction to PrismUI
                            </h2>
                            
                            <div className="prose prose-lg dark:prose-invert max-w-none">
                                <p className="text-gray-600 dark:text-gray-300 mb-6">
                                    PrismUI is a comprehensive component library that extends the excellent foundation provided by shadcn/ui. 
                                    Our philosophy centers around giving developers complete ownership and control over their code, 
                                    rather than hiding complexity behind abstractions.
                                </p>

                                <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">Inspiration</h3>
                                <p className="text-gray-600 dark:text-gray-300 mb-6">
                                    This project draws heavy inspiration from shadcn/ui's approach to component libraries and 
                                    Steven Tey's design philosophy. We believe in providing beautiful, accessible components 
                                    that you can copy, paste, and customize to fit your exact needs.
                                </p>

                                <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">Philosophy</h3>
                                <div className="bg-gradient-to-br from-primary/10 to-purple-400/10 rounded-lg p-6 mb-6">
                                    <ul className="space-y-3 text-gray-600 dark:text-gray-300">
                                        <li>• <strong>Ownership:</strong> You own the code, not a dependency</li>
                                        <li>• <strong>Customization:</strong> Every component is fully customizable</li>
                                        <li>• <strong>Accessibility:</strong> Built with accessibility in mind from the ground up</li>
                                        <li>• <strong>Performance:</strong> Optimized for speed and efficiency</li>
                                        <li>• <strong>Developer Experience:</strong> Intuitive APIs and excellent TypeScript support</li>
                                    </ul>
                                </div>

                                <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">What's Included</h3>
                                <div className="grid gap-6 md:grid-cols-2 mb-8">
                                    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
                                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Pre-built Sections</h4>
                                        <p className="text-sm text-gray-600 dark:text-gray-300">Hero sections, feature grids, testimonials, and more</p>
                                    </div>
                                    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
                                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Complex UI Patterns</h4>
                                        <p className="text-sm text-gray-600 dark:text-gray-300">Navigation, forms, modals, and interactive components</p>
                                    </div>
                                    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
                                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Developer Tools</h4>
                                        <p className="text-sm text-gray-600 dark:text-gray-300">TypeScript support, theme customization, and utilities</p>
                                    </div>
                                    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
                                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Design System</h4>
                                        <p className="text-sm text-gray-600 dark:text-gray-300">Consistent spacing, typography, and color schemes</p>
                                    </div>
                                </div>

                                <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">Credits</h3>
                                <p className="text-gray-600 dark:text-gray-300 mb-4">
                                    This project wouldn't be possible without the amazing work of:
                                </p>
                                <ul className="space-y-2 text-gray-600 dark:text-gray-300 mb-8">
                                    <li>• <strong>shadcn/ui</strong> - For the excellent component foundation</li>
                                    <li>• <strong>Radix UI</strong> - For accessible component primitives</li>
                                    <li>• <strong>Tailwind CSS</strong> - For the utility-first CSS framework</li>
                                    <li>• <strong>Steven Tey</strong> - For design inspiration and philosophy</li>
                                    <li>• <strong>Vercel</strong> - For the deployment platform</li>
                                </ul>

                                <div className="flex gap-4">
                                    <Button
                                        Element="a"
                                        emphasis="primary"
                                        label="Get Started"
                                        to="/docs/installation"
                                        ui="max"
                                    />
                                    <Button
                                        Element="a"
                                        emphasis="secondary"
                                        label="View Components"
                                        to="/docs/components"
                                        ui="max"
                                    />
                                </div>
                            </div>
                        </div>
                    </Container>
                </div>
            </main>
            <Footer />
        </>
    );
}