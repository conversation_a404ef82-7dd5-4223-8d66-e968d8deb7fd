import Head from "next/head";
import HeaderSection from "../../components/HeaderSection";
import Container from "../../components/Container";
import Footer from "../../components/FooterSection";

export default function InstallationPage() {
    return (
        <>
            <Head>
                <title>Installation - PrismUI Documentation</title>
                <meta name="description" content="Learn how to install and set up PrismUI in your Next.js project" />
                <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
            </Head>
            <HeaderSection />
            <main className="mb-40">
                {/* Installation Hero Section */}
                <div className="relative overflow-hidden dark:bg-darker pt-40 xl:pt-36" id="installation">
                    <Container>
                        <div className="text-center">
                            <h1 className="text-5xl font-black dark:text-white md:text-6xl xl:text-7xl">
                                Installation
                            </h1>
                            <p className="mt-8 text-lg text-gray-700 dark:text-gray-100 max-w-4xl mx-auto">
                                Get started with PrismUI in your Next.js project with these simple setup steps.
                            </p>
                        </div>
                    </Container>
                </div>

                {/* Installation Steps */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="max-w-4xl mx-auto">
                            <div className="space-y-12">
                                {/* Step 1 */}
                                <div>
                                    <div className="flex items-center mb-4">
                                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white font-bold mr-4">1</div>
                                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Create Next.js project with shadcn/ui</h2>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                                        Start by creating a new Next.js project and setting up shadcn/ui as the foundation.
                                    </p>
                                    <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                                        <code className="text-green-400 text-sm">
                                            npx create-next-app@latest my-app --typescript --tailwind --eslint<br/>
                                            cd my-app<br/>
                                            npx shadcn-ui@latest init
                                        </code>
                                    </div>
                                </div>

                                {/* Step 2 */}
                                <div>
                                    <div className="flex items-center mb-4">
                                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white font-bold mr-4">2</div>
                                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Configure components.json</h2>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                                        Ensure your components.json file is properly configured for PrismUI compatibility.
                                    </p>
                                    <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                                        <code className="text-green-400 text-sm">
                                            {`{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "default",
  "rsc": true,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.js",
    "css": "app/globals.css",
    "baseColor": "slate",
    "cssVariables": true
  },
  "aliases": {
    "components": "@/components",
    "utils": "@/lib/utils"
  }
}`}
                                        </code>
                                    </div>
                                </div>

                                {/* Step 3 */}
                                <div>
                                    <div className="flex items-center mb-4">
                                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white font-bold mr-4">3</div>
                                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Install additional dependencies</h2>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                                        Install the additional packages required for PrismUI components.
                                    </p>
                                    <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                                        <code className="text-green-400 text-sm">
                                            npm install framer-motion lucide-react @radix-ui/react-icons
                                        </code>
                                    </div>
                                </div>

                                {/* Step 4 */}
                                <div>
                                    <div className="flex items-center mb-4">
                                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white font-bold mr-4">4</div>
                                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Install base shadcn/ui components</h2>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                                        Add the essential shadcn/ui components that PrismUI builds upon.
                                    </p>
                                    <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                                        <code className="text-green-400 text-sm">
                                            npx shadcn-ui@latest add button card input label textarea select
                                        </code>
                                    </div>
                                </div>

                                {/* Step 5 */}
                                <div>
                                    <div className="flex items-center mb-4">
                                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white font-bold mr-4">5</div>
                                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Install PrismUI components</h2>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                                        Copy the PrismUI components into your project. You can copy individual components or the entire library.
                                    </p>
                                    <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                                        <code className="text-green-400 text-sm">
                                            # Copy individual components<br/>
                                            curl -o components/HeroSection.tsx https://prismui.dev/components/HeroSection.tsx<br/>
                                            curl -o components/Features.tsx https://prismui.dev/components/Features.tsx<br/><br/>
                                            # Or clone the entire repository<br/>
                                            git clone https://github.com/prismui/components.git
                                        </code>
                                    </div>
                                </div>

                                {/* Next Steps */}
                                <div className="bg-gradient-to-br from-primary/10 to-purple-400/10 rounded-lg p-8">
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">🎉 You're all set!</h3>
                                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                                        You can now start using PrismUI components in your project. Import them like any other React component:
                                    </p>
                                    <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                                        <code className="text-green-400 text-sm">
                                            {`import HeroSection from '@/components/HeroSection'
import Features from '@/components/Features'

export default function HomePage() {
  return (
    <main>
      <HeroSection />
      <Features />
    </main>
  )
}`}
                                        </code>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Container>
                </div>
            </main>
            <Footer />
        </>
    );
}