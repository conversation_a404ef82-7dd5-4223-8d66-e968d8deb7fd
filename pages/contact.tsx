import Head from "next/head";
import HeaderSection from "../components/HeaderSection";
import Container from "../components/Container";
import Button from "../components/Button";
import Footer from "../components/FooterSection";

export default function ContactPage() {
    return (
        <>
            <Head>
                <title>Contact NACE Certification - Request Your Quote</title>
                <meta name="description" content="Get in touch with NACE Certification for your ISO and SOC certification needs. Request a quote or verify a certificate." />
                <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
            </Head>
            <HeaderSection />
            <main className="mb-40">
                {/* Contact Hero Section */}
                <div className="relative overflow-hidden dark:bg-darker pt-40 xl:pt-36" id="contact">
                    <Container>
                        <div className="text-center">
                            <h1 className="text-5xl font-black dark:text-white md:text-6xl xl:text-7xl">
                                Ready to Get Certified?
                            </h1>
                            <p className="mt-8 text-lg text-gray-700 dark:text-gray-100 max-w-4xl mx-auto">
                                Start your certification journey today. Our experts are ready to guide you through the process and answer any questions you may have.
                            </p>
                        </div>
                    </Container>
                </div>

                {/* Contact Form Section */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="grid gap-12 lg:grid-cols-2">
                            {/* Contact Form */}
                            <div>
                                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
                                    Request a Quote
                                </h2>
                                <form className="space-y-6">
                                    <div className="grid gap-6 md:grid-cols-2">
                                        <div>
                                            <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                First Name *
                                            </label>
                                            <input
                                                type="text"
                                                id="firstName"
                                                name="firstName"
                                                required
                                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                                            />
                                        </div>
                                        <div>
                                            <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                Last Name *
                                            </label>
                                            <input
                                                type="text"
                                                id="lastName"
                                                name="lastName"
                                                required
                                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                                            />
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Email Address *
                                        </label>
                                        <input
                                            type="email"
                                            id="email"
                                            name="email"
                                            required
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                                        />
                                    </div>

                                    <div>
                                        <label htmlFor="company" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Company Name *
                                        </label>
                                        <input
                                            type="text"
                                            id="company"
                                            name="company"
                                            required
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                                        />
                                    </div>

                                    <div>
                                        <label htmlFor="standards" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Standards of Interest *
                                        </label>
                                        <select
                                            id="standards"
                                            name="standards"
                                            required
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                                        >
                                            <option value="">Select a standard</option>
                                            <option value="iso9001">ISO 9001 (Quality Management)</option>
                                            <option value="iso27001">ISO 27001 (Information Security)</option>
                                            <option value="iso14001">ISO 14001 (Environmental Management)</option>
                                            <option value="iso45001">ISO 45001 (Occupational Health & Safety)</option>
                                            <option value="soc2">SOC 2 Type II</option>
                                            <option value="integrated">Integrated Audit (Multiple Standards)</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label htmlFor="employees" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Number of Employees
                                        </label>
                                        <select
                                            id="employees"
                                            name="employees"
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                                        >
                                            <option value="">Select range</option>
                                            <option value="1-10">1-10</option>
                                            <option value="11-50">11-50</option>
                                            <option value="51-200">51-200</option>
                                            <option value="201-500">201-500</option>
                                            <option value="500+">500+</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label htmlFor="message" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Additional Information
                                        </label>
                                        <textarea
                                            id="message"
                                            name="message"
                                            rows={4}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                                            placeholder="Tell us about your certification goals, timeline, or any specific requirements..."
                                        ></textarea>
                                    </div>

                                    <div className="flex justify-start">
                                        <Button
                                            Element="button"
                                            emphasis="primary"
                                            label="Submit Request"
                                            type="submit"
                                            ui="max"
                                        />
                                    </div>
                                </form>
                            </div>

                            {/* Contact Information */}
                            <div>
                                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
                                    Get in Touch
                                </h2>
                                
                                <div className="space-y-8">
                                    <div>
                                        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                            Contact Information
                                        </h3>
                                        <div className="space-y-4">
                                            <div className="flex items-start space-x-3">
                                                <svg className="w-6 h-6 text-primary mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                                </svg>
                                                <div>
                                                    <p className="font-medium text-gray-900 dark:text-white">Email</p>
                                                    <p className="text-gray-600 dark:text-gray-300"><EMAIL></p>
                                                </div>
                                            </div>
                                            <div className="flex items-start space-x-3">
                                                <svg className="w-6 h-6 text-primary mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                                </svg>
                                                <div>
                                                    <p className="font-medium text-gray-900 dark:text-white">Phone</p>
                                                    <p className="text-gray-600 dark:text-gray-300">+****************</p>
                                                </div>
                                            </div>
                                            <div className="flex items-start space-x-3">
                                                <svg className="w-6 h-6 text-primary mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                                </svg>
                                                <div>
                                                    <p className="font-medium text-gray-900 dark:text-white">Headquarters</p>
                                                    <p className="text-gray-600 dark:text-gray-300">United States</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                            Global Coverage
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                                            Operating across five continents with 50+ lead auditors in 15 time zones.
                                        </p>
                                        <div className="grid grid-cols-2 gap-4 text-sm">
                                            <div>
                                                <p className="font-medium text-gray-900 dark:text-white">North America</p>
                                                <p className="font-medium text-gray-900 dark:text-white">Europe</p>
                                                <p className="font-medium text-gray-900 dark:text-white">Asia Pacific</p>
                                            </div>
                                            <div>
                                                <p className="font-medium text-gray-900 dark:text-white">South America</p>
                                                <p className="font-medium text-gray-900 dark:text-white">Africa</p>
                                                <p className="font-medium text-gray-900 dark:text-white">Middle East</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="bg-primary/10 rounded-lg p-6">
                                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                                            Certificate Verification
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                                            Need to verify a NACE certificate? Use our instant verification system.
                                        </p>
                                        <Button
                                            Element="a"
                                            emphasis="secondary"
                                            label="Verify Certificate"
                                            to="#verify"
                                            ui="max"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Container>
                </div>
            </main>
            <Footer />
        </>
    );
}