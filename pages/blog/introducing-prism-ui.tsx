import Head from "next/head";
import HeaderSection from "../../components/HeaderSection";
import Container from "../../components/Container";
import Button from "../../components/Button";
import Footer from "../../components/FooterSection";

export default function IntroducingPrismUIPost() {
    return (
        <>
            <Head>
                <title>Introducing Prism UI – A Modern Component Library Built on shadcn/ui</title>
                <meta name="description" content="Overview of PrismUI features and capabilities, including pre-built page sections, complex UI patterns, and developer experience features." />
                <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
            </Head>
            <HeaderSection />
            <main className="mb-40">
                {/* Post Hero Section */}
                <div className="relative overflow-hidden dark:bg-darker pt-40 xl:pt-36">
                    <Container>
                        <div className="max-w-4xl mx-auto text-center">
                            <div className="flex items-center justify-center text-sm text-gray-500 dark:text-gray-400 mb-4">
                                <span>March 15, 2024</span>
                                <span className="mx-2">•</span>
                                <span>5 min read</span>
                            </div>
                            <h1 className="text-4xl font-black dark:text-white md:text-5xl xl:text-6xl">
                                Introducing Prism UI – A Modern Component Library Built on shadcn/ui
                            </h1>
                            <p className="mt-8 text-lg text-gray-700 dark:text-gray-100">
                                Overview of PrismUI features and capabilities, including pre-built page sections, complex UI patterns, and developer experience features.
                            </p>
                        </div>
                    </Container>
                </div>

                {/* Post Content */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="max-w-4xl mx-auto">
                            <div className="prose prose-lg dark:prose-invert max-w-none">
                                <p className="text-gray-600 dark:text-gray-300 mb-6">
                                    Today, we're excited to introduce PrismUI – a comprehensive component library that extends the excellent foundation provided by shadcn/ui. Built with modern web development practices in mind, PrismUI offers a collection of beautiful, accessible, and highly customizable components.
                                </p>

                                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">What is PrismUI?</h2>
                                <p className="text-gray-600 dark:text-gray-300 mb-6">
                                    PrismUI is more than just another component library. It's a complete design system that provides:
                                </p>

                                <div className="bg-gradient-to-br from-primary/10 to-purple-400/10 rounded-lg p-6 mb-8">
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Key Components</h3>
                                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                                        <li>• <strong>Pre-built Page Sections:</strong> Hero sections, feature grids, testimonials, pricing tables, and more</li>
                                        <li>• <strong>Complex UI Patterns:</strong> Navigation systems, forms, modals, and interactive components</li>
                                        <li>• <strong>Developer Experience Features:</strong> TypeScript support, theme customization, and comprehensive documentation</li>
                                    </ul>
                                </div>

                                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">Why PrismUI?</h2>
                                <p className="text-gray-600 dark:text-gray-300 mb-6">
                                    We built PrismUI because we believe developers should have complete control over their code. Unlike traditional component libraries that hide complexity behind abstractions, PrismUI gives you the source code directly.
                                </p>

                                <div className="grid gap-6 md:grid-cols-2 mb-8">
                                    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
                                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2">🎨 Beautiful by Default</h4>
                                        <p className="text-sm text-gray-600 dark:text-gray-300">Every component is designed with modern aesthetics and follows current design trends.</p>
                                    </div>
                                    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
                                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2">♿ Accessibility First</h4>
                                        <p className="text-sm text-gray-600 dark:text-gray-300">Built with WCAG guidelines in mind, ensuring your applications are accessible to everyone.</p>
                                    </div>
                                    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
                                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2">🚀 Performance Optimized</h4>
                                        <p className="text-sm text-gray-600 dark:text-gray-300">Lightweight components that don't compromise on functionality or user experience.</p>
                                    </div>
                                    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
                                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2">🔧 Fully Customizable</h4>
                                        <p className="text-sm text-gray-600 dark:text-gray-300">Modify any component to match your brand and design requirements perfectly.</p>
                                    </div>
                                </div>

                                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">Getting Started</h2>
                                <p className="text-gray-600 dark:text-gray-300 mb-6">
                                    Getting started with PrismUI is straightforward. Simply copy the components you need into your project and start customizing. No complex installation process, no hidden dependencies – just clean, readable code that you can understand and modify.
                                </p>

                                <div className="bg-gray-900 rounded-lg p-6 mb-8">
                                    <code className="text-green-400 text-sm">
                                        {`import HeroSection from '@/components/HeroSection'
import Features from '@/components/Features'

export default function HomePage() {
  return (
    <main>
      <HeroSection />
      <Features />
    </main>
  )
}`}
                                    </code>
                                </div>

                                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">What's Next?</h2>
                                <p className="text-gray-600 dark:text-gray-300 mb-8">
                                    We're continuously expanding PrismUI with new components, patterns, and features. Our roadmap includes advanced form components, data visualization elements, and enhanced animation capabilities.
                                </p>

                                <div className="flex gap-4">
                                    <Button
                                        Element="a"
                                        emphasis="primary"
                                        label="Get Started"
                                        to="/docs/installation"
                                        ui="max"
                                    />
                                    <Button
                                        Element="a"
                                        emphasis="secondary"
                                        label="View Components"
                                        to="/docs"
                                        ui="max"
                                    />
                                </div>
                            </div>
                        </div>
                    </Container>
                </div>
            </main>
            <Footer />
        </>
    );
}