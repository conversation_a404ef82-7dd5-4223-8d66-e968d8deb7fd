import Head from "next/head";
import HeaderSection from "../../components/HeaderSection";
import Container from "../../components/Container";
import Card from "../../components/Card";
import Button from "../../components/Button";
import Footer from "../../components/FooterSection";

export default function BlogIndexPage() {
    const posts = [
        {
            title: "Introducing Prism UI – A Modern Component Library Built on shadcn/ui",
            excerpt: "Overview of PrismUI features and capabilities, including pre-built page sections, complex UI patterns, and developer experience features.",
            date: "March 15, 2024",
            readTime: "5 min read",
            slug: "introducing-prism-ui"
        },
        {
            title: "Getting Started with Prism UI",
            excerpt: "Installation and basic usage guide covering prerequisites, Node.js, Next.js, TypeScript requirements, and usage examples.",
            date: "March 10, 2024",
            readTime: "3 min read",
            slug: "getting-started"
        },
        {
            title: "Components Overview",
            excerpt: "Complete collection of available components including page sections, navigation components, and customization options.",
            date: "March 5, 2024",
            readTime: "7 min read",
            slug: "components-overview"
        }
    ];

    return (
        <>
            <Head>
                <title>Blog - PrismUI</title>
                <meta name="description" content="Latest updates, tutorials, and insights about PrismUI component library" />
                <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
            </Head>
            <HeaderSection />
            <main className="mb-40">
                {/* Blog Hero Section */}
                <div className="relative overflow-hidden dark:bg-darker pt-40 xl:pt-36" id="blog">
                    <Container>
                        <div className="text-center">
                            <h1 className="text-5xl font-black dark:text-white md:text-6xl xl:text-7xl">
                                PrismUI Blog
                            </h1>
                            <p className="mt-8 text-lg text-gray-700 dark:text-gray-100 max-w-4xl mx-auto">
                                Latest updates, tutorials, and insights about building beautiful user interfaces with PrismUI.
                            </p>
                        </div>
                    </Container>
                </div>

                {/* Blog Posts */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                            {posts.map((post, index) => (
                                <Card key={index}>
                                    <div>
                                        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-3">
                                            <span>{post.date}</span>
                                            <span className="mx-2">•</span>
                                            <span>{post.readTime}</span>
                                        </div>
                                        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                                            {post.title}
                                        </h2>
                                        <p className="text-gray-600 dark:text-gray-400 mb-6">
                                            {post.excerpt}
                                        </p>
                                        <Button
                                            Element="a"
                                            emphasis="secondary"
                                            label="Read More"
                                            to={`/blog/${post.slug}`}
                                            ui="max"
                                        />
                                    </div>
                                </Card>
                            ))}
                        </div>
                    </Container>
                </div>
            </main>
            <Footer />
        </>
    );
}