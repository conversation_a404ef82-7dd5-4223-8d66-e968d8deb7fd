import Head from "next/head";
import HeaderSection from "../components/HeaderSection";
import Container from "../components/Container";
import Button from "../components/Button";
import Footer from "../components/FooterSection";

export default function UpdatesPage() {
    return (
        <>
            <Head>
                <title>Quick Updates - NACE Certification</title>
                <meta name="description" content="Get instant updates about NACE Certification. Join our Discord for the latest news and updates!" />
                <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
            </Head>
            <HeaderSection />
            <main className="mb-40">
                {/* Updates Hero Section */}
                <div className="relative overflow-hidden dark:bg-darker pt-40 xl:pt-36" id="updates">
                    <Container>
                        <div className="text-center">
                            <h1 className="text-5xl font-black dark:text-white md:text-6xl xl:text-7xl">
                                Quick Updates Page
                            </h1>
                            <p className="mt-8 text-lg text-gray-700 dark:text-gray-100 max-w-4xl mx-auto">
                                Due to high demand for project updates, we've created this temporary page. While we're working on a more comprehensive solution, join our Discord for the latest news and updates!
                            </p>
                        </div>
                    </Container>
                </div>

                {/* Discord Integration Section */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="max-w-4xl mx-auto">
                            <div className="bg-gradient-to-br from-indigo-500 to-purple-600 rounded-3xl p-8 md:p-12 text-white text-center">
                                <div className="text-6xl mb-6">💬</div>
                                <h2 className="text-3xl font-bold mb-6">Join Our Discord Community</h2>
                                <p className="text-lg mb-8 opacity-90">
                                    Connect with fellow certification professionals, get real-time updates, and access exclusive resources.
                                </p>

                                <div className="grid gap-6 md:grid-cols-3 mb-8">
                                    <div className="bg-white/10 rounded-lg p-6">
                                        <div className="text-3xl mb-2">⚡</div>
                                        <h3 className="font-semibold mb-2">Get Instant Updates</h3>
                                        <p className="text-sm opacity-80">Be the first to know about new features, standards, and announcements in real-time.</p>
                                    </div>
                                    <div className="bg-white/10 rounded-lg p-6">
                                        <div className="text-3xl mb-2">🤝</div>
                                        <h3 className="font-semibold mb-2">Connect Directly</h3>
                                        <p className="text-sm opacity-80">Engage with our team and community members for support and networking.</p>
                                    </div>
                                    <div className="bg-white/10 rounded-lg p-6">
                                        <div className="text-3xl mb-2">🎯</div>
                                        <h3 className="font-semibold mb-2">Be First to Know</h3>
                                        <p className="text-sm opacity-80">Get early access to new features, beta programs, and exclusive content.</p>
                                    </div>
                                </div>

                                <Button
                                    Element="a"
                                    emphasis="secondary"
                                    label="Join Discord Server"
                                    to="https://discord.gg/nacecertification"
                                    ui="max"
                                />
                            </div>
                        </div>
                    </Container>
                </div>

                {/* Alternative Updates Section */}
                <div className="pt-32 md:pt-44">
                    <Container>
                        <div className="text-center mb-16">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white md:text-4xl lg:text-5xl">
                                Stay Updated
                            </h2>
                            <p className="mt-4 text-gray-600 dark:text-gray-300">
                                Multiple ways to stay connected with NACE Certification updates and news.
                            </p>
                        </div>

                        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 text-center">
                                <div className="text-4xl mb-4">📧</div>
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Email Newsletter</h3>
                                <p className="text-gray-600 dark:text-gray-300 mb-4">Monthly updates on industry trends and NACE news</p>
                                <Button
                                    Element="a"
                                    emphasis="secondary"
                                    label="Subscribe"
                                    to="/newsletter"
                                    ui="max"
                                />
                            </div>

                            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 text-center">
                                <div className="text-4xl mb-4">📱</div>
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Social Media</h3>
                                <p className="text-gray-600 dark:text-gray-300 mb-4">Follow us for daily updates and insights</p>
                                <Button
                                    Element="a"
                                    emphasis="secondary"
                                    label="Follow Us"
                                    to="/social"
                                    ui="max"
                                />
                            </div>

                            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 text-center">
                                <div className="text-4xl mb-4">📚</div>
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Blog & Resources</h3>
                                <p className="text-gray-600 dark:text-gray-300 mb-4">In-depth articles and educational content</p>
                                <Button
                                    Element="a"
                                    emphasis="secondary"
                                    label="Read Blog"
                                    to="/blog"
                                    ui="max"
                                />
                            </div>
                        </div>
                    </Container>
                </div>
            </main>
            <Footer />
        </>
    );
}