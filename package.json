{"name": "astrolus", "version": "0.0.1", "private": true, "scripts": {"build": "next build", "dev": "next dev"}, "dependencies": {"@next/font": "^14.2.15", "@types/react-dom": "^19.1.7", "@types/react-slick": "^0.23.13", "next": "^15.4.5", "react": "^19.1.1", "react-dom": "^19.1.1", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1"}, "devDependencies": {"@types/node": "^24.1.0", "@types/react": "^19.1.9", "postcss": "^8.5.6", "tailwindcss": "3.4.17", "typescript": "^5.8.3"}, "main": "index.js", "license": "ISC", "author": "Tailus UI", "description": "Modern and beautiful landing page built with tailwindcss and nextjs"}