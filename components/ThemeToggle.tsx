import React, { useState, useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';

const ThemeToggle: React.FC = () => {
  const [localTheme, setLocalTheme] = useState<'light' | 'dark'>('light');
  const [mounted, setMounted] = useState(false);

  // Try to use context, fallback to local state
  let theme = localTheme;
  let toggleTheme = () => {
    const newTheme = localTheme === 'light' ? 'dark' : 'light';
    setLocalTheme(newTheme);

    // Apply theme to document
    const root = document.documentElement;
    // Remove any existing theme classes first
    root.classList.remove('dark', 'light');

    if (newTheme === 'dark') {
      root.classList.add('dark');
      console.log('Added dark class to html element');
    } else {
      root.classList.add('light');
      console.log('Added light class to html element');
    }
    console.log('Current html classes:', root.className);

    // Also set the color-scheme meta property
    const metaColorScheme = document.querySelector('meta[name="color-scheme"]');
    if (metaColorScheme) {
      metaColorScheme.setAttribute('content', newTheme);
    } else {
      const meta = document.createElement('meta');
      meta.name = 'color-scheme';
      meta.content = newTheme;
      document.head.appendChild(meta);
    }

    // Save to localStorage
    try {
      localStorage.setItem('theme', newTheme);
    } catch (error) {
      // Ignore localStorage errors
    }
  };

  // Try to use context if available
  try {
    const contextValue = useTheme();
    if (contextValue && contextValue.theme) {
      theme = contextValue.theme;
      toggleTheme = contextValue.toggleTheme;
    }
  } catch (error) {
    console.log('Using fallback theme toggle');
  }

  // Initialize theme
  useEffect(() => {
    try {
      const savedTheme = localStorage.getItem('theme') as 'light' | 'dark';
      if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
        setLocalTheme(savedTheme);
        const root = document.documentElement;
        if (savedTheme === 'dark') {
          root.classList.add('dark');
        } else {
          root.classList.remove('dark');
        }
      }
    } catch (error) {
      // Ignore localStorage errors
    }
    setMounted(true);
  }, []);

  console.log('ThemeToggle rendered with theme:', theme, 'mounted:', mounted);

  return (
    <button
      onClick={() => {
        console.log('Theme toggle clicked! Current theme:', theme);
        toggleTheme();
      }}
      className="relative flex h-9 w-9 items-center justify-center rounded-full border-2 border-primary bg-white transition-colors duration-200 hover:bg-gray-50 dark:border-primary dark:bg-gray-800 dark:hover:bg-gray-700 shadow-lg hover:scale-105"
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
      title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
      disabled={!mounted}
    >
      {/* Always show sun icon initially to prevent hydration mismatch */}
      {mounted ? (
        <>
          {/* Sun icon for light mode */}
          <svg
            className={`h-4 w-4 transition-all duration-200 ${
              theme === 'light' ? 'rotate-0 scale-100' : 'rotate-90 scale-0'
            } absolute text-yellow-500`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth={2}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
            />
          </svg>

          {/* Moon icon for dark mode */}
          <svg
            className={`h-4 w-4 transition-all duration-200 ${
              theme === 'dark' ? 'rotate-0 scale-100' : '-rotate-90 scale-0'
            } absolute text-slate-700 dark:text-slate-300`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth={2}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
            />
          </svg>
        </>
      ) : (
        /* Default sun icon for SSR */
        <svg
          className="h-4 w-4 text-yellow-500"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          strokeWidth={2}
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
          />
        </svg>
      )}
    </button>
  );
};

export default ThemeToggle;
