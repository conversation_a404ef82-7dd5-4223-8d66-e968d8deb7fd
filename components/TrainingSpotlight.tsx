import Container from "./Container";
import Card from "./Card";
import Button from "./Button";

export default function TrainingSpotlight() {
    const trainingPrograms = [
        {
            title: "ISO Lead Auditor Courses",
            description: "Comprehensive training programs for aspiring lead auditors across all major ISO standards.",
            features: ["ISO 9001 Lead Auditor", "ISO 27001 Lead Auditor", "ISO 14001 Lead Auditor", "ISO 45001 Lead Auditor"]
        },
        {
            title: "Virtual & On-site Training",
            description: "Flexible delivery options to suit your schedule and learning preferences.",
            features: ["Live Virtual Sessions", "On-site Corporate Training", "Hybrid Learning Options", "Global Time Zone Coverage"]
        },
        {
            title: "Self-paced Learning",
            description: "Learn at your own pace with our comprehensive online learning platform.",
            features: ["Interactive Modules", "Progress Tracking", "Digital Certificates", "24/7 Access"]
        }
    ];

    return (
        <div className="pt-32 md:pt-44" id="training">
            <Container>
                <div className="mx-auto md:w-3/5">
                    <h2 className="text-center text-3xl font-bold text-gray-900 dark:text-white md:text-4xl lg:text-5xl">
                        Professional Development Programs
                    </h2>
                    <p className="mt-4 text-center text-gray-600 dark:text-gray-300">
                        Advance your career with our comprehensive lead auditor training programs and professional development courses.
                    </p>
                </div>

                <div className="mt-16 md:mt-20 grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {trainingPrograms.map((program, index) => (
                        <Card key={index}>
                            <div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                    {program.title}
                                </h3>
                                <p className="text-gray-600 dark:text-gray-400 mb-6">
                                    {program.description}
                                </p>
                                <ul className="space-y-2 mb-6">
                                    {program.features.map((feature, idx) => (
                                        <li key={idx} className="flex items-center space-x-2">
                                            <div className="w-2 h-2 bg-primary rounded-full"></div>
                                            <span className="text-sm text-gray-600 dark:text-gray-300">{feature}</span>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </Card>
                    ))}
                </div>

                <div className="mt-12 text-center">
                    <Button
                        Element="a"
                        emphasis="primary"
                        label="Explore Training Programs"
                        to="/services#training"
                        ui="max"
                    />
                </div>
            </Container>
        </div>
    );
}