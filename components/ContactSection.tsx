import Container from "./Container";
import Button from "./Button";

export default function ContactSection() {
    return (
        <div className="pt-32 md:pt-44" id="contact-home">
            <Container>
                <div className="relative rounded-3xl bg-primary/10 p-8 md:p-12 text-center overflow-hidden border border-primary/20 dark:border-gray-700">
                    <div className="relative z-10">
                        <div className="flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-12">
                            <div className="lg:w-1/2 text-center lg:text-left">
                                <h2 className="text-3xl font-bold md:text-4xl lg:text-5xl mb-4 text-primary dark:text-white">
                                    Ready to Start Your Certification Journey?
                                </h2>
                                <p className="text-lg md:text-xl mb-8 max-w-2xl text-gray-700 dark:text-gray-300">
                                    Get a personalized quote for your certification needs. Our experts are ready to guide you through the process and answer any questions.
                                </p>
                                
                                <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mt-4">
                                    <Button
                                        Element="a"
                                        emphasis="secondary"
                                        label="Request a Quote"
                                        to="/contact"
                                        ui="max"
                                    />
                                    <Button
                                        Element="a"
                                        emphasis="secondary"
                                        label="Verify Certificate"
                                        to="#verify"
                                        ui="max"
                                    />
                                </div>
                            </div>
                            
                            <div className="lg:w-1/2">
                                <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 text-center">
                                    <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700">
                                        <div className="text-2xl font-semibold">8-12 Weeks</div>
                                        <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">Typical Certification Timeline</div>
                                    </div>
                                    <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700">
                                        <div className="text-2xl font-semibold">50+ Auditors</div>
                                        <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">Global Expert Network</div>
                                    </div>
                                    <div className="bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700">
                                        <div className="text-2xl font-semibold">24/7 Portal</div>
                                        <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">Real-time Tracking</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </Container>
        </div>
    );
}