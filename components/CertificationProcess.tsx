import Container from "./Container";

export default function CertificationProcess() {
    const steps = [
        {
            number: "1",
            title: "<PERSON><PERSON><PERSON> & Scope",
            description: "We confirm objectives, locations, and boundaries"
        },
        {
            number: "2", 
            title: "Readiness Review (Stage 1)",
            description: "Document study & gap analysis"
        },
        {
            number: "3",
            title: "On-Site / Remote Audit (Stage 2)", 
            description: "Process verification & evidence sampling"
        },
        {
            number: "4",
            title: "Certification Decision",
            description: "Independent technical review, digital certificate issuance"
        },
        {
            number: "5",
            title: "Surveillance & Renewal",
            description: "Annual check-ups and a recertification audit at year 3"
        }
    ];

    return (
        <div className="pt-32 md:pt-44 bg-white text-gray-900 transition-colors dark:bg-darker dark:text-gray-100" id="process">
            <Container>
                <div className="mx-auto md:w-3/5">
                    <h2 className="text-center text-3xl font-bold text-gray-900 dark:text-white md:text-4xl lg:text-5xl">
                        The Path to Certification
                    </h2>
                    <p className="mt-4 text-center text-gray-600 dark:text-gray-300">
                        5 Simple Steps to achieve your certification goals with transparency and efficiency.
                    </p>
                </div>

                <div className="mt-16 md:mt-20">
                    <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                        {steps.map((step, index) => (
                            <div key={index} className="relative">
                                <div className="flex py-4 items-start space-x-4">
                                    <div className="flex-shrink-0">
                                        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary text-white font-bold text-lg">
                                            {step.number}
                                        </div>
                                    </div>
                                    <div className="flex-1">
                                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                                            {step.title}
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-400">
                                            {step.description}
                                        </p>
                                    </div>
                                </div>
                                {index < steps.length - 1 && (
                                    <div className="hidden lg:block absolute top-6 left-full w-8 h-0.5 bg-gray-300 dark:bg-gray-600 transform -translate-x-4"></div>
                                )}
                            </div>
                        ))}
                    </div>
                </div>
            </Container>
        </div>
    );
}