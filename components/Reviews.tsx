import Container from "./Container";
import Card from "./Card";
import Image from "next/image.js";
import Slider from "react-slick";

const testimonials = [
    {
        avatar: "/images/avatars/avatar.webp",
        name: "<PERSON>",
        title: "CTO, FinTech Scale-up",
        text: "NACE's portal cut our evidence-collection time in half. We sailed through ISO 27001. The digital-first approach and real-time tracking made our certification journey transparent and efficient."
    },
    {
        avatar: "/images/avatars/avatar-1.webp",
        name: "Dr. <PERSON><PERSON>",
        title: "Quality Director, Healthcare Systems Inc.",
        text: "The digital-first approach and real-time tracking made our certification journey transparent and efficient. NACE's impartial and professional approach gave us confidence throughout the process."
    },
    {
        avatar: "/images/avatars/avatar-1.webp",
        name: "<PERSON><PERSON>",
        title: "Quality Director, Manufacturing Corp",
        text: "The audit process was thorough yet smooth. NACE's global auditor network provided local expertise with international standards. Outstanding service for our multi-site operations."
    },
    {
        avatar: "/images/avatars/avatar-3.webp",
        name: "<PERSON>",
        title: "IT Security Manager, Digital Services Inc.",
        text: "Outstanding service for ISO 27001:2022 certification. The integrated audit approach saved us time and resources while maintaining the highest standards of rigor."
    },
    {
        avatar: "/images/avatars/avatar-2.webp",
        name: "Rajesh Patel",
        title: "General Manager, Industrial Solutions",
        text: "NACE's impartial and professional approach gave us confidence throughout the certification process. The speed and rigor combination is exactly what we needed."
    },
    {
        avatar: "/images/avatars/avatar.webp",
        name: "Dr. Meera Singh",
        title: "Academic Director, Education Institute",
        text: "The ISO 21001:2018 certification process was handled expertly. NACE's educational sector expertise made all the difference."
    },
    {
        avatar: "/images/avatars/avatar-4.webp",
        name: "Daniella Doe",
        title: "@hundler",
        text: "Illum aliquid quo deleniti aperiam ab veniam sint non cumque quis tempore cupiditate. Sint libero voluptas veniam at reprehenderit, veritatis harum et rerum."
    },
    {
        avatar: "/images/avatars/avatar.webp",
        name: "Daniella Doe",
        title: "@hundler",
        text: "Illum aliquid quo deleniti aperiam ab veniam Sint libero voluptas veniam at reprehenderit, veritatis harum et rerum."
    }
];

export default function Reviews() {
    const settings = {
        dots: true,
        infinite: true,
        speed: 500,
        slidesToShow: 3,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 3000,
        responsive: [
            {
                breakpoint: 1024,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1,
                    infinite: true,
                    dots: true
                }
            },
            {
                breakpoint: 600,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    initialSlide: 1
                }
            }
        ]
    };

    return (
        <div className="pt-32 md:pt-20 text-gray-600 dark:text-gray-300" id="reviews">
            <Container>
                <div className="mx-auto md:w-3/5">
                    <h2 className="text-center text-3xl font-bold text-gray-900 dark:text-white md:text-4xl lg:text-5xl">Client Voices</h2>
                    <p className="mt-4 text-center text-gray-600 dark:text-gray-300">Real feedback from organizations that have transformed compliance into competitive advantage with NACE.</p>
                </div>
                <div className="mt-12 md:mt-20">
                    <Slider {...settings}>
                        {testimonials.map((testimonial, index) => (
                            <div key={index} className="p-6">
                                <Card>
                                    <div className="flex gap-4">
                                        <Image className="h-12 w-12 rounded-full" src={testimonial.avatar} alt="user avatar" width="400" height="400" loading="lazy" />
                                        <div>
                                            <h3 className="text-lg font-medium text-gray-700 dark:text-white">{testimonial.name}</h3>
                                            <p className="text-sm text-gray-500 dark:text-gray-300">{testimonial.title}</p>
                                        </div>
                                    </div>
                                    <p>{testimonial.text}</p>
                                </Card>
                            </div>
                        ))}
                    </Slider>
                </div>
            </Container>
        </div>
    )
}