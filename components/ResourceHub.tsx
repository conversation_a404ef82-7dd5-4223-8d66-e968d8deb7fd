import Container from "./Container";
import Button from "./Button";

export default function ResourceHub() {
    const resources = [
        {
            title: "Certification Guides",
            description: "Step-by-step guides for each certification standard",
            icon: (
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="m-auto h-6 w-6 text-gray-700 dark:text-white">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
                </svg>
            ),
            items: ["ISO 9001 Implementation Guide", "ISO 27001 Security Framework", "SOC 2 Readiness Checklist"]
        },
        {
            title: "Templates & Tools",
            description: "Ready-to-use templates and assessment tools",
            icon: (
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="m-auto h-6 w-6 text-gray-700 dark:text-white">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 12a7.5 7.5 0 0015 0m-15 0a7.5 7.5 0 1115 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077l1.41-.513m14.095-5.13l1.41-.513M5.106 17.785l1.15-.964m11.49-9.642l1.149-.964M7.501 19.795l.75-1.3m7.5-12.99l.75-1.3m-6.063 16.658.26-1.477m2.605-14.772l.26-1.477m0 17.726l-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205L12 12m6.894 5.785l-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864l-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495" />
                </svg>
            ),
            items: ["Policy Templates", "Risk Assessment Tools", "Audit Checklists"]
        },
        {
            title: "Webinars & Events",
            description: "Educational webinars and industry events",
            icon: (
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="m-auto h-6 w-6 text-gray-700 dark:text-white">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 10.5l4.72-4.72a.75.75 0 011.28.53v11.38a.75.75 0 01-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 002.25-2.25v-9a2.25 2.25 0 00-2.25-2.25h-9A2.25 2.25 0 002.25 7.5v9a2.25 2.25 0 002.25 2.25z" />
                </svg>
            ),
            items: ["Monthly Webinar Series", "Industry Conferences", "Expert Panel Discussions"]
        },
        {
            title: "White Papers",
            description: "In-depth analysis and industry insights",
            icon: (
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="m-auto h-6 w-6 text-gray-700 dark:text-white">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                </svg>
            ),
            items: ["AI Governance Trends", "Cybersecurity Best Practices", "Compliance ROI Studies"]
        }
    ];

    return (
        <div className="pt-32 md:pt-44" id="resources">
            <Container>
                <div className="mx-auto md:w-3/5">
                    <h2 className="text-center text-3xl font-bold text-gray-900 dark:text-white md:text-4xl lg:text-5xl">
                        Resource Hub
                    </h2>
                    <p className="mt-4 text-center text-gray-600 dark:text-gray-300">
                        Access our comprehensive library of resources, guides, and educational materials to support your certification journey.
                    </p>
                </div>

                <div className="mt-16 md:mt-20">
                    <div className="relative grid rounded-3xl border border-gray-200 p-1 dark:border-gray-800 lg:grid-cols-2">
                        <div className="absolute inset-0 hidden h-max dark:block lg:my-auto">
                            <div aria-hidden="true" className="grid grid-cols-2 -space-x-52 opacity-50 dark:opacity-70 2xl:mx-auto 2xl:max-w-6xl">
                                <div className="h-60 bg-gradient-to-br from-primary to-purple-400 blur-3xl dark:from-blue-700"></div>
                                <div className="h-72 rounded-full bg-gradient-to-r from-cyan-400 to-sky-300 blur-3xl dark:from-transparent dark:to-indigo-600"></div>
                            </div>
                        </div>
                        <div>
                            <div className="relative flex h-full flex-col items-center justify-center gap-6 p-8 py-16 lg:py-8">
                                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-primary to-purple-400 p-1">
                                    <div className="flex h-full w-full items-center justify-center rounded-full bg-white dark:bg-gray-900">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="h-8 w-8 text-primary">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
                                        </svg>
                                    </div>
                                </div>
                                <div className="mx-auto text-center sm:w-2/5">
                                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Knowledge Center</h2>
                                    <p className="mt-3 text-gray-600 dark:text-gray-400">Expert-curated resources to accelerate your certification success and compliance excellence.</p>
                                </div>
                            </div>
                        </div>
                        <div className="relative grid overflow-hidden rounded-[1.25rem] bg-gray-100 p-1 dark:bg-gray-800/50 sm:grid-cols-2">
                            {resources.map((resource, index) => (
                                <div key={index} className="flex flex-col gap-6 rounded-2xl p-8 transition duration-300 hover:bg-white hover:shadow-2xl hover:shadow-gray-600/10 dark:hover:bg-gray-700/60 dark:hover:shadow-none">
                                    <div className="flex h-10 w-10 rounded border border-gray-200 dark:border-gray-700">
                                        {resource.icon}
                                    </div>
                                    <div>
                                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                            {resource.title}
                                        </h3>
                                        <p className="mt-3 text-sm text-gray-600 dark:text-gray-400">
                                            {resource.description}
                                        </p>
                                        <ul className="mt-4 space-y-1 text-xs text-gray-500 dark:text-gray-400">
                                            {resource.items.map((item, idx) => (
                                                <li key={idx} className="flex items-start">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="mr-2 h-2 w-2 flex-shrink-0 text-primary">
                                                        <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                                                    </svg>
                                                    {item}
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>

                <div className="mt-12 text-center">
                    <Button
                        Element="a"
                        emphasis="secondary"
                        label="Access Resource Library"
                        to="/resources"
                        ui="max"
                    />
                </div>
            </Container>
        </div>
    );
}