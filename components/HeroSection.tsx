import Button from "./Button";
import Container from "./Container";
import Image from "next/image.js";

export default function HeroSection() {
  return (
    <div className="relative overflow-hidden dark:bg-darker lg:overflow-auto" id="home">
      <div className="absolute inset-x-0 top-32 lg:hidden">
        <div aria-hidden="true" className="grid grid-cols-2 -space-x-52 opacity-50 dark:opacity-60 2xl:mx-auto 2xl:max-w-6xl">
          <div className="h-60 bg-gradient-to-br from-primary to-purple-400 blur-3xl dark:from-blue-700"></div>
          <div className="h-72 rounded-full bg-gradient-to-r from-cyan-400 to-sky-300 blur-3xl dark:from-transparent dark:to-indigo-600"></div>
        </div>
      </div>
      <Container>
        <div className="relative ml-auto pt-40 xl:pt-36">
          <div className="gap-12 md:flex md:items-center">
            <div className="text-center sm:px-12 md:w-2/3 md:px-0 md:text-left lg:w-1/2">
              <div className="mb-6 inline-flex items-center rounded-full bg-primary/10 px-4 py-2 text-sm font-medium text-primary dark:bg-primary/20">
                Accredited by United Accreditation Foundation
              </div>
              <h1 className="text-5xl font-black dark:text-white md:text-6xl xl:text-7xl">Get Certified. Get Trust.</h1>
              <div className="">
                <p className="mt-8 text-lg text-gray-700 dark:text-gray-100">
                We deliver accredited ISO and SOC certifications that prove your business meets the highest standards—wherever you operate.
                </p>
                <div className="mt-8 mb-8 italic text-gray-600 dark:text-gray-300">
                "Compliance made simple—so you can focus on growth."
                </div>
                <div className="mt-12 flex justify-center gap-4 sm:gap-6 md:justify-start">
                  <Button
                    Element="a"
                    emphasis="primary"
                    label="Start Certification"
                    to="/contact"
                    ui="max"
                  />
                  <Button
                    Element="a"
                    emphasis="secondary"
                    label="Check a Certificate"
                    to="#verify"
                    ui="max"
                  />
                </div>
              </div>
            </div>
            <div className="relative mt-20 md:mt-0 md:w-2/5 lg:w-3/5">
              <div className="-ml-6 md:-mr-72 lg:mr-0">
                <Image 
                  className="h-full object-cover object-left dark:hidden"
                  src="/images/hero.webp"
                  alt="app screenshot"
                  width="1628"
                  height="1233"
                  priority={true}
                />
                <Image
                  className="hidden h-full object-cover object-left dark:block"
                  src="/images/hero-dark.webp"
                  alt="app screenshot"
                  width="1628"
                  height="1233"
                  priority={true}
                />
              </div>
            </div>
          </div>

          <div className="mt-16 text-center md:mt-32 lg:mt-24 xl:mt-20">
            <div className="inline-block rounded-full bg-primary/10 px-6 py-3">
              <span className="text-sm font-semibold text-primary dark:text-primary-light uppercase tracking-wide">
                Instant Trust At A Glance
              </span>
            </div>
            <div className="mt-6 grid grid-cols-2 gap-8 md:grid-cols-4">
              <div className="group relative overflow-hidden rounded-2xl bg-white p-6 shadow-lg transition-all duration-300 hover:shadow-xl dark:bg-gray-800/50">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-purple-600/10 opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
                <div className="relative">
                  <div className="text-4xl font-bold text-primary dark:text-white">3000+</div>
                  <div className="mt-2 text-sm font-medium text-gray-600 dark:text-gray-300">Certificates issued worldwide</div>
                </div>
              </div>
              <div className="group relative overflow-hidden rounded-2xl bg-white p-6 shadow-lg transition-all duration-300 hover:shadow-xl dark:bg-gray-800/50">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-purple-600/10 opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
                <div className="relative">
                  <div className="text-4xl font-bold text-primary dark:text-white">40+</div>
                  <div className="mt-2 text-sm font-medium text-gray-600 dark:text-gray-300">Accredited lead auditors</div>
                </div>
              </div>
              <div className="group relative overflow-hidden rounded-2xl bg-white p-6 shadow-lg transition-all duration-300 hover:shadow-xl dark:bg-gray-800/50">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-purple-600/10 opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
                <div className="relative">
                  <div className="text-4xl font-bold text-primary dark:text-white">15</div>
                  <div className="mt-2 text-sm font-medium text-gray-600 dark:text-gray-300">Time zones covered</div>
                </div>
              </div>
              <div className="group relative overflow-hidden rounded-2xl bg-white p-6 shadow-lg transition-all duration-300 hover:shadow-xl dark:bg-gray-800/50">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-purple-600/10 opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
                <div className="relative">
                  <div className="text-4xl font-bold text-primary dark:text-white">98%</div>
                  <div className="mt-2 text-sm font-medium text-gray-600 dark:text-gray-300">Client-renewal rate</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
}
